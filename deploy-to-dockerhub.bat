@echo off
REM Simple Docker Hub Push Script
REM Usage: deploy-to-dockerhub.bat [username] [repository] [tag]

setlocal enabledelayedexpansion

REM Default values
set REPOSITORY=vpsscripthelper-blueblue
set TAG=latest

REM Parse arguments
set USERNAME=%1
set REPOSITORY=%2
set TAG=%3

if "%REPOSITORY%"=="" set REPOSITORY=vpsscripthelper-blueblue
if "%TAG%"=="" set TAG=latest

echo.
echo === VPSScriptHelper-BlueBlue Docker Hub Push ===
echo.

REM Get username if not provided
if "%USERNAME%"=="" (
    set /p USERNAME="Enter your Docker Hub username: "
)

if "%USERNAME%"=="" (
    echo Error: Username is required
    pause
    exit /b 1
)

set IMAGE_NAME=%USERNAME%/%REPOSITORY%:%TAG%

echo Checking Docker...
docker info >nul 2>&1
if errorlevel 1 (
    echo Error: Docker is not running. Please start Docker Desktop.
    pause
    exit /b 1
)
echo OK: Docker is running

echo Checking project files...
if not exist "main.py" (
    echo Error: main.py not found. Run from project root.
    pause
    exit /b 1
)
echo OK: Project files found

echo Logging into Docker Hub...
docker login
if errorlevel 1 (
    echo Error: Failed to login to Docker Hub
    pause
    exit /b 1
)
echo OK: Logged into Docker Hub

echo Building image: %IMAGE_NAME%
docker build --platform linux/amd64 -t %IMAGE_NAME% .
if errorlevel 1 (
    echo Error: Failed to build image
    pause
    exit /b 1
)
echo OK: Image built successfully

echo Pushing to Docker Hub...
docker push %IMAGE_NAME%
if errorlevel 1 (
    echo Error: Failed to push to Docker Hub
    pause
    exit /b 1
)

echo.
echo === SUCCESS ===
echo Image: %IMAGE_NAME%
echo Docker Hub: https://hub.docker.com/r/%USERNAME%/%REPOSITORY%
echo.
echo To run on Linux:
echo docker run -d -p 8000:8000 --name vpsscripthelper %IMAGE_NAME%
echo.
pause
endlocal
