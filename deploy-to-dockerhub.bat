@echo off
REM VPSScriptHelper-BlueBlue Docker Hub Deployment Script
REM Windows Batch script for deploying FastAPI application to Docker Hub
REM
REM Usage: deploy-to-dockerhub.bat [username] [repository] [version]
REM
REM Examples:
REM   deploy-to-dockerhub.bat
REM   deploy-to-dockerhub.bat myuser
REM   deploy-to-dockerhub.bat myuser my-repo
REM   deploy-to-dockerhub.bat myuser my-repo v1.0.0

setlocal enabledelayedexpansion

REM Script configuration
set SCRIPT_NAME=VPSScriptHelper-BlueBlue Docker Deployment
set LOG_FILE=deployment-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.log
set LOG_FILE=!LOG_FILE: =0!

REM Default values
set DEFAULT_REPOSITORY=vpsscripthelper-blueblue
set DEFAULT_VERSION=latest

REM Parse command line arguments
set DOCKERHUB_USERNAME=%1
set REPOSITORY=%2
set VERSION=%3

if "%REPOSITORY%"=="" set REPOSITORY=%DEFAULT_REPOSITORY%
if "%VERSION%"=="" set VERSION=%DEFAULT_VERSION%

REM Colors (for Windows 10+)
set COLOR_SUCCESS=0A
set COLOR_WARNING=0E
set COLOR_ERROR=0C
set COLOR_INFO=0B

echo.
echo ========================================
echo %SCRIPT_NAME%
echo ========================================
echo.

REM Logging function
call :log "Starting deployment script" "INFO"
call :log "Log file: %LOG_FILE%" "INFO"

REM Check prerequisites
call :check_prerequisites
if errorlevel 1 goto :error_exit

REM Get Docker Hub credentials
call :get_dockerhub_credentials
if errorlevel 1 goto :error_exit

REM Validate project structure
call :validate_project_structure
if errorlevel 1 goto :error_exit

REM Build Docker image
call :build_docker_image
if errorlevel 1 goto :error_exit

REM Push to Docker Hub
call :push_to_dockerhub
if errorlevel 1 goto :error_exit

REM Success
call :deployment_success
goto :end

REM Functions

:log
set MESSAGE=%~1
set LEVEL=%~2
set TIMESTAMP=%date% %time%
echo [%TIMESTAMP%] [%LEVEL%] %MESSAGE%
echo [%TIMESTAMP%] [%LEVEL%] %MESSAGE% >> %LOG_FILE%
goto :eof

:log_success
call :log "%~1" "SUCCESS"
color %COLOR_SUCCESS%
timeout /t 1 /nobreak >nul
color
goto :eof

:log_warning
call :log "%~1" "WARNING"
color %COLOR_WARNING%
timeout /t 1 /nobreak >nul
color
goto :eof

:log_error
call :log "%~1" "ERROR"
color %COLOR_ERROR%
timeout /t 2 /nobreak >nul
color
goto :eof

:log_info
call :log "%~1" "INFO"
color %COLOR_INFO%
timeout /t 1 /nobreak >nul
color
goto :eof

:check_prerequisites
call :log_info "Checking prerequisites..."

REM Check if Docker is installed
docker --version >nul 2>&1
if errorlevel 1 (
    call :log_error "Docker is not installed or not in PATH"
    call :log_error "Please install Docker Desktop and ensure it's running"
    exit /b 1
)
call :log_success "Docker is installed"

REM Check if Docker daemon is running
docker info >nul 2>&1
if errorlevel 1 (
    call :log_error "Docker daemon is not running"
    call :log_error "Please start Docker Desktop"
    exit /b 1
)
call :log_success "Docker daemon is running"

REM Check if we're in the correct directory
if not exist "main.py" (
    call :log_error "main.py not found"
    call :log_error "Please run this script from the project root directory"
    exit /b 1
)
call :log_success "Project directory validated"

call :log_success "All prerequisites met"
goto :eof

:get_dockerhub_credentials
call :log_info "Getting Docker Hub credentials..."

REM Get username if not provided
if "%DOCKERHUB_USERNAME%"=="" (
    set /p DOCKERHUB_USERNAME="Enter your Docker Hub username: "
)

if "%DOCKERHUB_USERNAME%"=="" (
    call :log_error "Docker Hub username is required"
    exit /b 1
)

REM Check for password in environment variable
if not "%DOCKERHUB_PASSWORD%"=="" (
    call :log_info "Using password from environment variable"
    goto :docker_login
)

REM Prompt for password
call :log_info "Enter your Docker Hub password/token (input will be hidden):"
call :get_password

:docker_login
call :log_info "Logging into Docker Hub..."
echo %DOCKERHUB_PASSWORD% | docker login --username %DOCKERHUB_USERNAME% --password-stdin >nul 2>&1
if errorlevel 1 (
    call :log_error "Failed to login to Docker Hub"
    call :log_error "Please check your credentials"
    exit /b 1
)
call :log_success "Successfully logged into Docker Hub"
goto :eof

:get_password
REM Simple password input (characters will be visible, but better than nothing)
set /p DOCKERHUB_PASSWORD="Password: "
if "%DOCKERHUB_PASSWORD%"=="" (
    call :log_error "Password is required"
    exit /b 1
)
goto :eof

:validate_project_structure
call :log_info "Validating project structure..."

set REQUIRED_FILES=main.py requirements.txt Dockerfile .env.example
set REQUIRED_DIRS=app

for %%f in (%REQUIRED_FILES%) do (
    if not exist "%%f" (
        call :log_error "Required file missing: %%f"
        exit /b 1
    )
)

for %%d in (%REQUIRED_DIRS%) do (
    if not exist "%%d" (
        call :log_error "Required directory missing: %%d"
        exit /b 1
    )
)

call :log_success "Project structure validation passed"
goto :eof

:build_docker_image
call :log_info "Building Docker image..."

set IMAGE_NAME=%DOCKERHUB_USERNAME%/%REPOSITORY%
set FULL_IMAGE_NAME=%IMAGE_NAME%:%VERSION%

call :log_info "Building image: %FULL_IMAGE_NAME%"

docker build --platform linux/amd64 -t %FULL_IMAGE_NAME% . >nul 2>&1
if errorlevel 1 (
    call :log_error "Failed to build Docker image"
    call :log_error "Check the Dockerfile and project structure"
    exit /b 1
)

call :log_success "Docker image built successfully: %FULL_IMAGE_NAME%"

REM Show image info
call :log_info "Built image information:"
docker images %IMAGE_NAME% --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

goto :eof

:push_to_dockerhub
call :log_info "Pushing image to Docker Hub..."

set IMAGE_NAME=%DOCKERHUB_USERNAME%/%REPOSITORY%
set FULL_IMAGE_NAME=%IMAGE_NAME%:%VERSION%

call :log_info "Pushing: %FULL_IMAGE_NAME%"

docker push %FULL_IMAGE_NAME% >nul 2>&1
if errorlevel 1 (
    call :log_error "Failed to push to Docker Hub"
    call :log_error "Check your internet connection and Docker Hub permissions"
    exit /b 1
)

call :log_success "Successfully pushed: %FULL_IMAGE_NAME%"
goto :eof

:deployment_success
echo.
call :log_success "Deployment completed successfully!"
echo.
call :log_info "Deployment Summary:"
echo   Repository: %DOCKERHUB_USERNAME%/%REPOSITORY%
echo   Version: %VERSION%
echo   Docker Hub URL: https://hub.docker.com/r/%DOCKERHUB_USERNAME%/%REPOSITORY%
echo.
call :log_info "To run the container:"
echo   docker run -d -p 8000:8000 --name vpsscripthelper %DOCKERHUB_USERNAME%/%REPOSITORY%:%VERSION%
echo.
call :log_info "To run with custom environment:"
echo   docker run -d -p 8000:8000 -e SECRET_KEY=your-secret-key --name vpsscripthelper %DOCKERHUB_USERNAME%/%REPOSITORY%:%VERSION%
echo.
call :log_info "Log file saved: %LOG_FILE%"
goto :eof

:error_exit
call :log_error "Deployment failed. Check the log file: %LOG_FILE%"
echo.
echo Press any key to exit...
pause >nul
exit /b 1

:end
echo.
echo Press any key to exit...
pause >nul
endlocal
