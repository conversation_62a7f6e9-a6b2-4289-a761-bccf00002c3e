@echo off
REM Super Simple Docker Hub Push Script

echo === VPSScriptHelper-BlueBlue Docker Hub Push ===

REM Get username
set /p USERNAME="Enter your Docker Hub username: "
if "%USERNAME%"=="" (
    echo Error: Username is required
    pause
    exit /b 1
)

set IMAGE_NAME=%USERNAME%/vpsscripthelper-blueblue:latest

REM Check Docker
echo Checking Docker...
docker info >nul 2>&1
if errorlevel 1 (
    echo Error: Docker is not running
    pause
    exit /b 1
)

REM Check files
if not exist "main.py" (
    echo Error: main.py not found
    pause
    exit /b 1
)

REM Login
echo Logging into Docker Hub...
docker login
if errorlevel 1 (
    echo Error: Login failed
    pause
    exit /b 1
)

REM Build
echo Building image...
docker build --platform linux/amd64 -t %IMAGE_NAME% .
if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

REM Push
echo Pushing to Docker Hub...
docker push %IMAGE_NAME%
if errorlevel 1 (
    echo Error: Push failed
    pause
    exit /b 1
)

echo.
echo SUCCESS! Image pushed: %IMAGE_NAME%
echo.
echo To run on Linux:
echo docker run -d -p 8000:8000 --name vpsscripthelper %IMAGE_NAME%
echo.
pause
