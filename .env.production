# Production Environment Configuration for <PERSON><PERSON><PERSON><PERSON>elper-BlueBlue
# Copy this file to .env and update the values for your production environment

# Environment
ENVIRONMENT=production

# Application Settings
APP_NAME=VPSScriptHelper-BlueBlue
DEBUG=false
HOST=0.0.0.0
PORT=8000

# Security Settings - CHANGE THESE IN PRODUCTION!
SECRET_KEY=CHANGE-THIS-TO-A-SECURE-RANDOM-KEY-IN-PRODUCTION
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database Settings
# For SQLite (default)
DATABASE_URL=sqlite+aiosqlite:///./data/blueblue.db

# For PostgreSQL (recommended for production)
# DATABASE_URL=postgresql+asyncpg://username:password@postgres:5432/blueblue

# CORS Settings (restrict for production)
ALLOWED_HOSTS=yourdomain.com,localhost,127.0.0.1

# SSH Settings
SSH_TIMEOUT=30
SSH_RETRY_ATTEMPTS=3
SSH_RETRY_DELAY=5

# Xray Configuration
XRAY_CONFIG_PATH=/etc/xray/config.json
XRAY_SERVICE_NAME=xray
XRAY_BACKUP_PATH=/etc/xray/config.json.backup

# Task Scheduling
EXPIRY_CHECK_INTERVAL_HOURS=24
AUTO_REMOVE_EXPIRED=true

# Logging
LOG_LEVEL=WARNING
LOG_FILE=

# File Paths (container paths)
UPLOAD_DIR=/app/data/uploads
BACKUP_DIR=/app/data/backups

# PostgreSQL Settings (if using PostgreSQL)
POSTGRES_DB=blueblue
POSTGRES_USER=blueblue
POSTGRES_PASSWORD=CHANGE-THIS-PASSWORD

# SSL/TLS Settings (for nginx)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Monitoring and Health Check Settings
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# Rate Limiting (handled by nginx, but can be used by app)
API_RATE_LIMIT=100
LOGIN_RATE_LIMIT=5

# Backup Settings
BACKUP_RETENTION_DAYS=30
AUTO_BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *

# Email Settings (for notifications - optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true
ADMIN_EMAIL=

# Sentry Error Tracking (optional)
SENTRY_DSN=

# Redis Settings (for caching - optional)
REDIS_URL=redis://redis:6379/0
