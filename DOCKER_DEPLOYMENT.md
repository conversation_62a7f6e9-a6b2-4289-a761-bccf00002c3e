# Docker Deployment Guide for VPSScriptHelper-BlueBlue

This guide provides comprehensive instructions for deploying the VPSScriptHelper-BlueBlue FastAPI application to Docker Hub and running it in production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start](#quick-start)
3. [Deployment Scripts](#deployment-scripts)
4. [Manual Deployment](#manual-deployment)
5. [Running the Container](#running-the-container)
6. [Environment Configuration](#environment-configuration)
7. [Production Considerations](#production-considerations)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Development Environment (Windows)
- **Docker Desktop**: Install and ensure it's running
- **PowerShell 5.1+** or **Command Prompt**: For running deployment scripts
- **Docker Hub Account**: Create account at [hub.docker.com](https://hub.docker.com)
- **Git**: For version control (optional but recommended)

### Verify Prerequisites
```powershell
# Check Docker installation
docker --version
docker info

# Check PowerShell version
$PSVersionTable.PSVersion
```

## Quick Start

### Option 1: PowerShell Script (Recommended)
```powershell
# Basic deployment
.\deploy-to-dockerhub.ps1

# Deploy with specific version
.\deploy-to-dockerhub.ps1 -Version "v1.0.0" -AdditionalTag "latest"

# Deploy with credentials (avoid password prompt)
$env:DOCKERHUB_PASSWORD = "your-token-here"
.\deploy-to-dockerhub.ps1 -DockerHubUsername "yourusername"
```

### Option 2: Batch Script
```cmd
# Basic deployment
deploy-to-dockerhub.bat

# With parameters
deploy-to-dockerhub.bat yourusername yourrepo v1.0.0
```

## Deployment Scripts

### PowerShell Script Features
- **Comprehensive error handling** with detailed logging
- **Interactive credential input** with secure password handling
- **Cross-platform Docker builds** (Windows → Linux)
- **Multi-tag support** for versioning
- **Verbose output options** for debugging
- **Prerequisites validation** before deployment

#### PowerShell Script Parameters
```powershell
.\deploy-to-dockerhub.ps1 [OPTIONS]

OPTIONS:
    -DockerHubUsername    Docker Hub username
    -DockerHubPassword    Docker Hub password/token
    -Repository           Repository name (default: vpsscripthelper-blueblue)
    -Version              Image version tag (default: latest)
    -AdditionalTag        Additional version tag (e.g., v1.0.0)
    -SkipBuild           Skip the Docker build step
    -SkipPush            Skip the Docker push step
    -Verbose             Show detailed output
```

### Batch Script Features
- **Simple command-line interface** for basic deployments
- **Automatic credential prompting**
- **Basic error handling** with colored output
- **Deployment logging** with timestamps

## Manual Deployment

If you prefer manual control over the deployment process:

### Step 1: Build the Docker Image
```bash
# Build for Linux deployment (cross-platform)
docker build --platform linux/amd64 -t yourusername/vpsscripthelper-blueblue:latest .

# Build with specific version
docker build --platform linux/amd64 -t yourusername/vpsscripthelper-blueblue:v1.0.0 .
```

### Step 2: Tag the Image (Optional)
```bash
# Create additional tags
docker tag yourusername/vpsscripthelper-blueblue:v1.0.0 yourusername/vpsscripthelper-blueblue:latest
```

### Step 3: Login to Docker Hub
```bash
# Login interactively
docker login

# Or with credentials
echo "your-password" | docker login --username yourusername --password-stdin
```

### Step 4: Push to Docker Hub
```bash
# Push specific version
docker push yourusername/vpsscripthelper-blueblue:v1.0.0

# Push latest
docker push yourusername/vpsscripthelper-blueblue:latest
```

## Running the Container

### Basic Usage
```bash
# Run with default settings
docker run -d -p 8000:8000 --name vpsscripthelper yourusername/vpsscripthelper-blueblue:latest

# Run with custom environment
docker run -d -p 8000:8000 \
  -e SECRET_KEY=your-secure-secret-key \
  -e ENVIRONMENT=production \
  --name vpsscripthelper \
  yourusername/vpsscripthelper-blueblue:latest
```

### With Persistent Data
```bash
# Create volumes for persistent data
docker volume create vpsscripthelper_data
docker volume create vpsscripthelper_logs

# Run with volumes
docker run -d -p 8000:8000 \
  -v vpsscripthelper_data:/app/data \
  -v vpsscripthelper_logs:/app/logs \
  -e SECRET_KEY=your-secure-secret-key \
  --name vpsscripthelper \
  yourusername/vpsscripthelper-blueblue:latest
```

### Using Docker Compose
```bash
# Start with default configuration
docker-compose up -d

# Start with production profile (includes Nginx)
docker-compose --profile production up -d

# Start with PostgreSQL
docker-compose --profile postgres up -d
```

## Environment Configuration

### Required Environment Variables
```bash
SECRET_KEY=your-very-secure-secret-key-change-this-in-production
```

### Optional Environment Variables
```bash
# Application settings
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=8000

# Security settings
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database settings
DATABASE_URL=sqlite+aiosqlite:///./data/blueblue.db

# CORS settings
ALLOWED_HOSTS=yourdomain.com,localhost

# SSH settings
SSH_TIMEOUT=30
SSH_RETRY_ATTEMPTS=3
SSH_RETRY_DELAY=5

# Logging
LOG_LEVEL=WARNING
```

### Environment File Example
Create a `.env` file for Docker Compose:
```bash
# .env file for Docker Compose
SECRET_KEY=your-very-secure-secret-key
ALLOWED_HOSTS=yourdomain.com,localhost
LOG_LEVEL=INFO
POSTGRES_DB=blueblue
POSTGRES_USER=blueblue
POSTGRES_PASSWORD=secure-db-password
```

## Production Considerations

### Security
1. **Change default SECRET_KEY**: Generate a secure random key
2. **Use HTTPS**: Configure SSL/TLS certificates
3. **Restrict CORS origins**: Set specific allowed hosts
4. **Use secrets management**: For sensitive environment variables
5. **Regular updates**: Keep base images and dependencies updated

### Performance
1. **Resource limits**: Set appropriate CPU and memory limits
2. **Health checks**: Configure proper health check endpoints
3. **Logging**: Use structured logging for production
4. **Monitoring**: Implement application monitoring

### High Availability
1. **Load balancing**: Use multiple container instances
2. **Database**: Use external database (PostgreSQL) for production
3. **Reverse proxy**: Use Nginx or similar for SSL termination
4. **Backup strategy**: Regular database and configuration backups

### Example Production Docker Compose
```yaml
version: '3.8'
services:
  app:
    image: yourusername/vpsscripthelper-blueblue:latest
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    environment:
      - SECRET_KEY=${SECRET_KEY}
      - DATABASE_URL=postgresql+asyncpg://user:pass@postgres:5432/blueblue
    depends_on:
      - postgres
  
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl
  
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=blueblue
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
```

## Troubleshooting

### Common Issues

#### Docker Build Fails
```bash
# Check Dockerfile syntax
docker build --no-cache -t test .

# Check build context size
docker system df
```

#### Container Won't Start
```bash
# Check container logs
docker logs vpsscripthelper

# Check container status
docker ps -a

# Inspect container configuration
docker inspect vpsscripthelper
```

#### Permission Issues
```bash
# Check file permissions in container
docker exec -it vpsscripthelper ls -la /app

# Run as specific user
docker run --user 1000:1000 yourusername/vpsscripthelper-blueblue:latest
```

#### Network Issues
```bash
# Check port binding
docker port vpsscripthelper

# Test connectivity
curl http://localhost:8000/api/v1/health
```

### Debugging Commands
```bash
# Enter container shell
docker exec -it vpsscripthelper /bin/bash

# Check environment variables
docker exec vpsscripthelper env

# Monitor resource usage
docker stats vpsscripthelper

# View real-time logs
docker logs -f vpsscripthelper
```

### Getting Help
1. Check the application logs: `docker logs vpsscripthelper`
2. Verify environment variables are set correctly
3. Ensure Docker daemon is running
4. Check firewall settings for port 8000
5. Verify Docker Hub credentials and permissions

For additional support, check the project's GitHub repository or create an issue with:
- Docker version: `docker --version`
- Container logs: `docker logs vpsscripthelper`
- Environment details: OS, Docker Desktop version
- Error messages and steps to reproduce
