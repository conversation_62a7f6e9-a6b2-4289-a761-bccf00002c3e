# VPSScriptHelper-BlueBlue Deployment Verification Script
# PowerShell script to verify Docker deployment is working correctly

param(
    [string]$ContainerName = "vpsscripthelper",
    [string]$Port = "8000",
    [string]$Host = "localhost",
    [int]$TimeoutSeconds = 30,
    [switch]$Verbose = $false
)

$ErrorActionPreference = "Stop"

# Colors for output
$ColorSuccess = "Green"
$ColorWarning = "Yellow"
$ColorError = "Red"
$ColorInfo = "Cyan"

function Write-Status {
    param(
        [string]$Message,
        [string]$Status = "INFO",
        [string]$Color = "White"
    )
    
    $Timestamp = Get-Date -Format "HH:mm:ss"
    Write-Host "[$Timestamp] [$Status] $Message" -ForegroundColor $Color
}

function Test-ContainerHealth {
    Write-Status "Verifying Docker deployment..." "INFO" $ColorInfo
    
    # Check if container exists and is running
    try {
        $containerStatus = docker ps --filter "name=$ContainerName" --format "{{.Status}}"
        if ([string]::IsNullOrEmpty($containerStatus)) {
            Write-Status "Container '$ContainerName' not found or not running" "ERROR" $ColorError
            
            # Check if container exists but is stopped
            $stoppedContainer = docker ps -a --filter "name=$ContainerName" --format "{{.Status}}"
            if (-not [string]::IsNullOrEmpty($stoppedContainer)) {
                Write-Status "Container exists but is stopped: $stoppedContainer" "WARNING" $ColorWarning
                Write-Status "Try: docker start $ContainerName" "INFO" $ColorInfo
            }
            return $false
        }
        
        Write-Status "Container is running: $containerStatus" "SUCCESS" $ColorSuccess
        return $true
    } catch {
        Write-Status "Failed to check container status: $($_.Exception.Message)" "ERROR" $ColorError
        return $false
    }
}

function Test-ApplicationHealth {
    Write-Status "Testing application health endpoints..." "INFO" $ColorInfo
    
    $baseUrl = "http://${Host}:${Port}"
    $endpoints = @(
        @{ Path = "/"; Name = "Root endpoint" },
        @{ Path = "/api/v1/health"; Name = "Health check" },
        @{ Path = "/docs"; Name = "API documentation" }
    )
    
    $allPassed = $true
    
    foreach ($endpoint in $endpoints) {
        try {
            Write-Status "Testing $($endpoint.Name): $baseUrl$($endpoint.Path)" "INFO" $ColorInfo
            
            $response = Invoke-WebRequest -Uri "$baseUrl$($endpoint.Path)" -TimeoutSec $TimeoutSeconds -UseBasicParsing
            
            if ($response.StatusCode -eq 200) {
                Write-Status "$($endpoint.Name) - OK (Status: $($response.StatusCode))" "SUCCESS" $ColorSuccess
                
                if ($Verbose -and $endpoint.Path -eq "/api/v1/health") {
                    $healthData = $response.Content | ConvertFrom-Json
                    Write-Status "Health details: $($healthData | ConvertTo-Json -Compress)" "INFO" $ColorInfo
                }
            } else {
                Write-Status "$($endpoint.Name) - Unexpected status: $($response.StatusCode)" "WARNING" $ColorWarning
                $allPassed = $false
            }
        } catch {
            Write-Status "$($endpoint.Name) - FAILED: $($_.Exception.Message)" "ERROR" $ColorError
            $allPassed = $false
        }
    }
    
    return $allPassed
}

function Test-ContainerLogs {
    Write-Status "Checking container logs for errors..." "INFO" $ColorInfo
    
    try {
        $logs = docker logs $ContainerName --tail 20 2>&1
        
        $errorLines = $logs | Where-Object { $_ -match "ERROR|CRITICAL|FATAL" }
        $warningLines = $logs | Where-Object { $_ -match "WARNING|WARN" }
        
        if ($errorLines) {
            Write-Status "Found error messages in logs:" "WARNING" $ColorWarning
            $errorLines | ForEach-Object { Write-Status "  $_" "ERROR" $ColorError }
        }
        
        if ($warningLines -and $Verbose) {
            Write-Status "Found warning messages in logs:" "INFO" $ColorInfo
            $warningLines | ForEach-Object { Write-Status "  $_" "WARNING" $ColorWarning }
        }
        
        if (-not $errorLines -and -not $warningLines) {
            Write-Status "No errors or warnings found in recent logs" "SUCCESS" $ColorSuccess
        }
        
        if ($Verbose) {
            Write-Status "Recent logs:" "INFO" $ColorInfo
            $logs | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        }
        
        return $true
    } catch {
        Write-Status "Failed to retrieve container logs: $($_.Exception.Message)" "ERROR" $ColorError
        return $false
    }
}

function Test-ContainerResources {
    Write-Status "Checking container resource usage..." "INFO" $ColorInfo
    
    try {
        $stats = docker stats $ContainerName --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
        
        Write-Status "Resource usage:" "INFO" $ColorInfo
        Write-Host $stats -ForegroundColor $ColorInfo
        
        return $true
    } catch {
        Write-Status "Failed to retrieve container stats: $($_.Exception.Message)" "ERROR" $ColorError
        return $false
    }
}

function Show-DeploymentInfo {
    Write-Status "Deployment Information:" "INFO" $ColorInfo
    
    try {
        # Container info
        $containerInfo = docker inspect $ContainerName --format "{{.Config.Image}}" 2>$null
        if ($containerInfo) {
            Write-Status "Image: $containerInfo" "INFO" $ColorInfo
        }
        
        # Port mapping
        $portMapping = docker port $ContainerName 2>$null
        if ($portMapping) {
            Write-Status "Port mapping: $portMapping" "INFO" $ColorInfo
        }
        
        # Environment variables (non-sensitive)
        if ($Verbose) {
            $envVars = docker exec $ContainerName env 2>$null | Where-Object { 
                $_ -notmatch "SECRET|PASSWORD|TOKEN|KEY" -and $_ -match "^[A-Z_]+=.*"
            }
            if ($envVars) {
                Write-Status "Environment variables:" "INFO" $ColorInfo
                $envVars | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
            }
        }
        
    } catch {
        Write-Status "Could not retrieve all deployment info: $($_.Exception.Message)" "WARNING" $ColorWarning
    }
}

function Main {
    Write-Status "Starting deployment verification for VPSScriptHelper-BlueBlue" "INFO" $ColorInfo
    Write-Status "Container: $ContainerName, URL: http://${Host}:${Port}" "INFO" $ColorInfo
    
    $results = @{
        ContainerHealth = Test-ContainerHealth
        ApplicationHealth = $false
        LogsCheck = $false
        ResourcesCheck = $false
    }
    
    if ($results.ContainerHealth) {
        $results.ApplicationHealth = Test-ApplicationHealth
        $results.LogsCheck = Test-ContainerLogs
        $results.ResourcesCheck = Test-ContainerResources
        Show-DeploymentInfo
    }
    
    Write-Status "Verification Summary:" "INFO" $ColorInfo
    $results.GetEnumerator() | ForEach-Object {
        $status = if ($_.Value) { "PASS" } else { "FAIL" }
        $color = if ($_.Value) { $ColorSuccess } else { $ColorError }
        Write-Status "  $($_.Key): $status" "RESULT" $color
    }
    
    $overallSuccess = ($results.Values | Where-Object { -not $_ }).Count -eq 0
    
    if ($overallSuccess) {
        Write-Status "Deployment verification completed successfully!" "SUCCESS" $ColorSuccess
        Write-Status "Application is ready at: http://${Host}:${Port}" "SUCCESS" $ColorSuccess
        Write-Status "API documentation: http://${Host}:${Port}/docs" "SUCCESS" $ColorSuccess
    } else {
        Write-Status "Deployment verification failed. Please check the issues above." "ERROR" $ColorError
        exit 1
    }
}

# Help function
if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "-h") {
    Write-Host @"
VPSScriptHelper-BlueBlue Deployment Verification Script

USAGE:
    .\verify-deployment.ps1 [OPTIONS]

OPTIONS:
    -ContainerName     Docker container name (default: vpsscripthelper)
    -Port              Application port (default: 8000)
    -Host              Application host (default: localhost)
    -TimeoutSeconds    HTTP request timeout (default: 30)
    -Verbose           Show detailed output
    -Help              Show this help message

EXAMPLES:
    # Basic verification
    .\verify-deployment.ps1

    # Custom container and port
    .\verify-deployment.ps1 -ContainerName "my-container" -Port "8080"

    # Verbose output
    .\verify-deployment.ps1 -Verbose

"@ -ForegroundColor $ColorInfo
    exit 0
}

# Run main function
Main
