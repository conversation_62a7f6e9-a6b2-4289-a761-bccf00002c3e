# VPSScriptHelper-BlueBlue Docker Hub Deployment Script
# PowerShell script for Windows development environment
# Builds and deploys FastAPI application to Docker Hub

param(
    [string]$DockerHubUsername = "",
    [string]$DockerHubPassword = "",
    [string]$Repository = "vpsscripthelper-blueblue",
    [string]$Version = "latest",
    [string]$AdditionalTag = "",
    [switch]$SkipBuild = $false,
    [switch]$SkipPush = $false,
    [switch]$Verbose = $false
)

# Script configuration
$ErrorActionPreference = "Stop"
$ScriptName = "VPSScriptHelper-BlueBlue Docker Deployment"
$LogFile = "deployment-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"

# Colors for output
$ColorSuccess = "Green"
$ColorWarning = "Yellow"
$ColorError = "Red"
$ColorInfo = "Cyan"

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO",
        [string]$Color = "White"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    
    Write-Host $LogMessage -ForegroundColor $Color
    Add-Content -Path $LogFile -Value $LogMessage
}

# Error handling function
function Handle-Error {
    param([string]$ErrorMessage)
    
    Write-Log "ERROR: $ErrorMessage" "ERROR" $ColorError
    Write-Log "Deployment failed. Check the log file: $LogFile" "ERROR" $ColorError
    exit 1
}

# Success function
function Write-Success {
    param([string]$Message)
    Write-Log $Message "SUCCESS" $ColorSuccess
}

# Warning function
function Write-Warning {
    param([string]$Message)
    Write-Log $Message "WARNING" $ColorWarning
}

# Info function
function Write-Info {
    param([string]$Message)
    Write-Log $Message "INFO" $ColorInfo
}

# Main deployment function
function Start-Deployment {
    Write-Log "Starting $ScriptName" "INFO" $ColorInfo
    Write-Log "Log file: $LogFile" "INFO" $ColorInfo
    
    # Validate prerequisites
    Test-Prerequisites
    
    # Get Docker Hub credentials if not provided
    Get-DockerHubCredentials
    
    # Validate project structure
    Test-ProjectStructure
    
    # Build Docker image
    if (-not $SkipBuild) {
        Build-DockerImage
    } else {
        Write-Warning "Skipping Docker build as requested"
    }
    
    # Push to Docker Hub
    if (-not $SkipPush) {
        Push-ToDockerHub
    } else {
        Write-Warning "Skipping Docker push as requested"
    }
    
    # Cleanup and summary
    Complete-Deployment
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check if Docker is installed and running
    try {
        $dockerVersion = docker --version
        Write-Success "Docker found: $dockerVersion"
    } catch {
        Handle-Error "Docker is not installed or not running. Please install Docker Desktop and ensure it's running."
    }
    
    # Check if Docker daemon is running
    try {
        docker info | Out-Null
        Write-Success "Docker daemon is running"
    } catch {
        Handle-Error "Docker daemon is not running. Please start Docker Desktop."
    }
    
    # Check if we're in the correct directory
    if (-not (Test-Path "main.py")) {
        Handle-Error "main.py not found. Please run this script from the project root directory."
    }
    
    Write-Success "All prerequisites met"
}

function Get-DockerHubCredentials {
    Write-Info "Configuring Docker Hub credentials..."
    
    # Get username if not provided
    if ([string]::IsNullOrEmpty($DockerHubUsername)) {
        $DockerHubUsername = Read-Host "Enter your Docker Hub username"
        if ([string]::IsNullOrEmpty($DockerHubUsername)) {
            Handle-Error "Docker Hub username is required"
        }
    }
    
    # Get password if not provided (check environment variable first)
    if ([string]::IsNullOrEmpty($DockerHubPassword)) {
        $DockerHubPassword = $env:DOCKERHUB_PASSWORD
        if ([string]::IsNullOrEmpty($DockerHubPassword)) {
            $SecurePassword = Read-Host "Enter your Docker Hub password or token" -AsSecureString
            $DockerHubPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))
        }
    }
    
    # Login to Docker Hub
    try {
        Write-Info "Logging into Docker Hub..."
        echo $DockerHubPassword | docker login --username $DockerHubUsername --password-stdin
        Write-Success "Successfully logged into Docker Hub"
    } catch {
        Handle-Error "Failed to login to Docker Hub. Please check your credentials."
    }
}

function Test-ProjectStructure {
    Write-Info "Validating project structure..."
    
    $RequiredFiles = @("main.py", "requirements.txt", "Dockerfile", ".env.example")
    $RequiredDirs = @("app")
    
    foreach ($file in $RequiredFiles) {
        if (-not (Test-Path $file)) {
            Handle-Error "Required file missing: $file"
        }
    }
    
    foreach ($dir in $RequiredDirs) {
        if (-not (Test-Path $dir -PathType Container)) {
            Handle-Error "Required directory missing: $dir"
        }
    }
    
    Write-Success "Project structure validation passed"
}

function Build-DockerImage {
    Write-Info "Building Docker image..."
    
    $ImageName = "$DockerHubUsername/$Repository"
    $FullImageName = "${ImageName}:${Version}"
    
    try {
        Write-Info "Building image: $FullImageName"
        
        if ($Verbose) {
            docker build --platform linux/amd64 -t $FullImageName .
        } else {
            docker build --platform linux/amd64 -t $FullImageName . | Out-Null
        }
        
        Write-Success "Docker image built successfully: $FullImageName"
        
        # Tag with additional version if provided
        if (-not [string]::IsNullOrEmpty($AdditionalTag)) {
            $AdditionalImageName = "${ImageName}:${AdditionalTag}"
            docker tag $FullImageName $AdditionalImageName
            Write-Success "Additional tag created: $AdditionalImageName"
        }
        
        # Show image info
        $ImageInfo = docker images $ImageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
        Write-Info "Built images:"
        Write-Host $ImageInfo
        
    } catch {
        Handle-Error "Failed to build Docker image: $($_.Exception.Message)"
    }
}

function Push-ToDockerHub {
    Write-Info "Pushing image to Docker Hub..."
    
    $ImageName = "$DockerHubUsername/$Repository"
    $FullImageName = "${ImageName}:${Version}"
    
    try {
        Write-Info "Pushing: $FullImageName"
        
        if ($Verbose) {
            docker push $FullImageName
        } else {
            docker push $FullImageName | Out-Null
        }
        
        Write-Success "Successfully pushed: $FullImageName"
        
        # Push additional tag if exists
        if (-not [string]::IsNullOrEmpty($AdditionalTag)) {
            $AdditionalImageName = "${ImageName}:${AdditionalTag}"
            Write-Info "Pushing: $AdditionalImageName"
            
            if ($Verbose) {
                docker push $AdditionalImageName
            } else {
                docker push $AdditionalImageName | Out-Null
            }
            
            Write-Success "Successfully pushed: $AdditionalImageName"
        }
        
    } catch {
        Handle-Error "Failed to push to Docker Hub: $($_.Exception.Message)"
    }
}

function Complete-Deployment {
    Write-Success "Deployment completed successfully!"
    
    $ImageName = "$DockerHubUsername/$Repository"
    
    Write-Info "Deployment Summary:"
    Write-Host "  Repository: $ImageName" -ForegroundColor $ColorInfo
    Write-Host "  Version: $Version" -ForegroundColor $ColorInfo
    if (-not [string]::IsNullOrEmpty($AdditionalTag)) {
        Write-Host "  Additional Tag: $AdditionalTag" -ForegroundColor $ColorInfo
    }
    Write-Host "  Docker Hub URL: https://hub.docker.com/r/$ImageName" -ForegroundColor $ColorInfo
    
    Write-Info "To run the container:"
    Write-Host "  docker run -d -p 8000:8000 --name vpsscripthelper ${ImageName}:${Version}" -ForegroundColor $ColorInfo
    
    Write-Info "To run with custom environment:"
    Write-Host "  docker run -d -p 8000:8000 -e SECRET_KEY=your-secret-key --name vpsscripthelper ${ImageName}:${Version}" -ForegroundColor $ColorInfo
    
    Write-Log "Log file saved: $LogFile" "INFO" $ColorInfo
}

# Help function
function Show-Help {
    Write-Host @"
VPSScriptHelper-BlueBlue Docker Hub Deployment Script

USAGE:
    .\deploy-to-dockerhub.ps1 [OPTIONS]

OPTIONS:
    -DockerHubUsername    Docker Hub username (will prompt if not provided)
    -DockerHubPassword    Docker Hub password/token (will prompt if not provided, or use DOCKERHUB_PASSWORD env var)
    -Repository           Repository name (default: vpsscripthelper-blueblue)
    -Version              Image version tag (default: latest)
    -AdditionalTag        Additional version tag (e.g., v1.0.0)
    -SkipBuild           Skip the Docker build step
    -SkipPush            Skip the Docker push step
    -Verbose             Show detailed output
    -Help                Show this help message

EXAMPLES:
    # Basic deployment
    .\deploy-to-dockerhub.ps1

    # Deploy with specific version
    .\deploy-to-dockerhub.ps1 -Version "v1.0.0" -AdditionalTag "latest"

    # Deploy with credentials
    .\deploy-to-dockerhub.ps1 -DockerHubUsername "myuser" -Repository "my-repo"

    # Only build, don't push
    .\deploy-to-dockerhub.ps1 -SkipPush

ENVIRONMENT VARIABLES:
    DOCKERHUB_PASSWORD    Docker Hub password/token (alternative to -DockerHubPassword)

"@ -ForegroundColor $ColorInfo
}

# Main execution
if ($args -contains "-Help" -or $args -contains "--help" -or $args -contains "-h") {
    Show-Help
    exit 0
}

try {
    Start-Deployment
} catch {
    Handle-Error "Unexpected error: $($_.Exception.Message)"
}
